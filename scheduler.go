package main

import (
	"sort"
	"time"
)

// NewSimulation creates a new simulation from configuration
func NewSimulation(config *Configuration, maxWaitTime time.Duration) *Simulation {
	stocks := make(map[string]int)
	for name, quantity := range config.InitialStocks {
		stocks[name] = quantity
	}

	return &Simulation{
		Stocks:            stocks,
		Processes:         config.Processes,
		OptimizeTargets:   config.OptimizeTargets,
		RunningProcesses:  []ProcessExecution{},
		CompletedSchedule: []ProcessExecution{},
		CurrentCycle:      0,
		MaxWaitTime:       maxWaitTime,
		StartTime:         time.Now(),
	}
}

// RunSimulation runs the simulation and returns the schedule result
func (s *Simulation) RunSimulation() *ScheduleResult {
	for {
		// Check if we've exceeded the maximum wait time
		if time.Since(s.StartTime) > s.MaxWaitTime {
			break
		}

		// Complete any finished processes
		s.completeFinishedProcesses()

		// Try to start new processes
		started := s.startAvailableProcesses()

		// If no processes are running and none can be started, we're done
		if len(s.RunningProcesses) == 0 && !started {
			break
		}

		// Advance to the next cycle
		s.CurrentCycle++
	}

	return &ScheduleResult{
		Schedule:    s.CompletedSchedule,
		FinalStocks: s.Stocks,
		LastCycle:   s.CurrentCycle,
		Error:       nil,
	}
}

// completeFinishedProcesses completes any processes that have finished
func (s *Simulation) completeFinishedProcesses() {
	var stillRunning []ProcessExecution

	for _, pe := range s.RunningProcesses {
		if s.CurrentCycle >= pe.EndTime {
			// Process is complete, add results to stocks
			for _, result := range pe.Process.Results {
				s.Stocks[result.Name] += result.Quantity
			}
			s.CompletedSchedule = append(s.CompletedSchedule, pe)
		} else {
			stillRunning = append(stillRunning, pe)
		}
	}

	s.RunningProcesses = stillRunning
}

// startAvailableProcesses tries to start as many processes as possible
func (s *Simulation) startAvailableProcesses() bool {
	started := false

	// Get processes sorted by priority
	processes := s.getPrioritizedProcesses()

	for _, process := range processes {
		if s.canStartProcess(&process) {
			s.startProcess(&process)
			started = true
		}
	}

	return started
}

// canStartProcess checks if a process can be started with current stocks
func (s *Simulation) canStartProcess(process *Process) bool {
	for _, need := range process.Needs {
		if s.Stocks[need.Name] < need.Quantity {
			return false
		}
	}
	return true
}

// startProcess starts a process and consumes required resources
func (s *Simulation) startProcess(process *Process) {
	// Consume required resources
	for _, need := range process.Needs {
		s.Stocks[need.Name] -= need.Quantity
	}

	// Create process execution
	pe := ProcessExecution{
		Process:   process,
		StartTime: s.CurrentCycle,
		EndTime:   s.CurrentCycle + process.Duration,
	}

	s.RunningProcesses = append(s.RunningProcesses, pe)
}

// getPrioritizedProcesses returns processes sorted by priority
func (s *Simulation) getPrioritizedProcesses() []Process {
	processes := make([]Process, len(s.Processes))
	copy(processes, s.Processes)

	// Sort processes by priority based on optimization targets
	sort.Slice(processes, func(i, j int) bool {
		return s.getProcessPriority(&processes[i]) > s.getProcessPriority(&processes[j])
	})

	return processes
}

// getProcessPriority calculates priority for a process based on optimization targets
func (s *Simulation) getProcessPriority(process *Process) int {
	priority := 0

	// Higher priority for processes that produce optimization targets
	for _, result := range process.Results {
		for _, target := range s.OptimizeTargets {
			if target == result.Name {
				priority += result.Quantity * 100
			}
		}
	}

	// Lower priority for longer processes (when optimizing time)
	for _, target := range s.OptimizeTargets {
		if target == "time" {
			priority -= process.Duration
		}
	}

	// Higher priority for processes that use fewer resources
	for _, need := range process.Needs {
		priority -= need.Quantity
	}

	return priority
}

// CanContinue checks if the simulation can continue
func (s *Simulation) CanContinue() bool {
	// If processes are still running, we can continue
	if len(s.RunningProcesses) > 0 {
		return true
	}

	// Check if any process can be started
	for _, process := range s.Processes {
		if s.canStartProcess(&process) {
			return true
		}
	}

	return false
}
