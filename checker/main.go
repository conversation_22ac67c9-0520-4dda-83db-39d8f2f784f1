package main

import (
	"bufio"
	"fmt"
	"os"
	"regexp"
	"sort"
	"strconv"
	"strings"
)

// Import the types and parser from the main package
// We'll need to copy the necessary types and functions here

// Resource represents a needed or produced resource in a process
type Resource struct {
	Name     string
	Quantity int
}

// Process represents a production process
type Process struct {
	Name     string
	Needs    []Resource
	Results  []Resource
	Duration int
}

// Configuration represents the parsed configuration file
type Configuration struct {
	InitialStocks   map[string]int
	Processes       []Process
	OptimizeTargets []string
}

// LogEntry represents a single entry in the log file
type LogEntry struct {
	Cycle   int
	Process string
}

func main() {
	if len(os.Args) != 3 {
		fmt.Println("Usage: ./checker <config_file> <log_file>")
		os.Exit(1)
	}

	configFile := os.Args[1]
	logFile := os.Args[2]

	// Parse configuration
	config, err := parseConfiguration(configFile)
	if err != nil {
		fmt.Printf("Error parsing configuration: %v\n", err)
		fmt.Println("Exiting...")
		os.Exit(1)
	}

	// Parse log file
	logEntries, err := parseLogFile(logFile)
	if err != nil {
		fmt.Printf("Error parsing log file: %v\n", err)
		fmt.Println("Exiting...")
		os.Exit(1)
	}

	// Validate the log against the configuration
	err = validateLog(config, logEntries)
	if err != nil {
		fmt.Println("Error detected")
		fmt.Println(err)
		fmt.Println("Exiting...")
		os.Exit(1)
	}

	fmt.Println("Trace completed, no error detected.")
}

// parseConfiguration parses a configuration file
func parseConfiguration(filename string) (*Configuration, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, fmt.Errorf("error opening file: %v", err)
	}
	defer file.Close()

	config := &Configuration{
		InitialStocks:   make(map[string]int),
		Processes:       []Process{},
		OptimizeTargets: []string{},
	}

	scanner := bufio.NewScanner(file)

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())

		// Skip empty lines and comments
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// Parse stock definitions
		if stockMatch := regexp.MustCompile(`^([a-zA-Z_][a-zA-Z0-9_]*):(\d+)$`).FindStringSubmatch(line); stockMatch != nil {
			name := stockMatch[1]
			quantity, _ := strconv.Atoi(stockMatch[2])
			config.InitialStocks[name] = quantity
			continue
		}

		// Parse optimize directive
		if strings.HasPrefix(line, "optimize:") {
			optimizeStr := strings.TrimPrefix(line, "optimize:")
			optimizeStr = strings.Trim(optimizeStr, "()")
			targets := strings.Split(optimizeStr, ";")
			for _, target := range targets {
				target = strings.TrimSpace(target)
				if target != "" {
					config.OptimizeTargets = append(config.OptimizeTargets, target)
				}
			}
			continue
		}

		// Parse process definitions
		if strings.Contains(line, ":") && strings.Contains(line, "(") && strings.Contains(line, ")") {
			process, err := parseProcess(line)
			if err == nil {
				config.Processes = append(config.Processes, *process)
			}
			continue
		}
	}

	return config, nil
}

// parseProcess parses a process definition line
func parseProcess(line string) (*Process, error) {
	processRegex := regexp.MustCompile(`^([a-zA-Z_][a-zA-Z0-9_]*):(\([^)]*\)):(\([^)]*\)):(\d+)$`)
	matches := processRegex.FindStringSubmatch(line)

	if matches == nil {
		return nil, fmt.Errorf("invalid process format")
	}

	name := matches[1]
	needsStr := strings.Trim(matches[2], "()")
	resultsStr := strings.Trim(matches[3], "()")
	duration, _ := strconv.Atoi(matches[4])

	needs, _ := parseResources(needsStr)
	results, _ := parseResources(resultsStr)

	return &Process{
		Name:     name,
		Needs:    needs,
		Results:  results,
		Duration: duration,
	}, nil
}

// parseResources parses a resource list
func parseResources(resourceStr string) ([]Resource, error) {
	var resources []Resource

	if resourceStr == "" {
		return resources, nil
	}

	parts := strings.Split(resourceStr, ";")
	for _, part := range parts {
		part = strings.TrimSpace(part)
		if part == "" {
			continue
		}

		resourceParts := strings.Split(part, ":")
		if len(resourceParts) != 2 {
			continue
		}

		name := strings.TrimSpace(resourceParts[0])
		quantity, _ := strconv.Atoi(strings.TrimSpace(resourceParts[1]))

		resources = append(resources, Resource{
			Name:     name,
			Quantity: quantity,
		})
	}

	return resources, nil
}

// parseLogFile parses a log file and returns log entries
func parseLogFile(filename string) ([]LogEntry, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, fmt.Errorf("error opening log file: %v", err)
	}
	defer file.Close()

	var entries []LogEntry
	scanner := bufio.NewScanner(file)

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())

		// Skip empty lines and comments
		if line == "" || strings.HasPrefix(line, "#") || strings.HasPrefix(line, "No more") {
			continue
		}

		// Parse log entry (cycle:process)
		parts := strings.SplitN(line, ":", 2)
		if len(parts) != 2 {
			continue
		}

		cycle, err := strconv.Atoi(parts[0])
		if err != nil {
			continue
		}

		entries = append(entries, LogEntry{
			Cycle:   cycle,
			Process: parts[1],
		})
	}

	return entries, nil
}

// validateLog validates the log entries against the configuration
func validateLog(config *Configuration, logEntries []LogEntry) error {
	// Initialize stocks
	stocks := make(map[string]int)
	for name, quantity := range config.InitialStocks {
		stocks[name] = quantity
	}

	// Create process map for quick lookup
	processMap := make(map[string]*Process)
	for i := range config.Processes {
		processMap[config.Processes[i].Name] = &config.Processes[i]
	}

	// Track running processes
	runningProcesses := make(map[int][]LogEntry) // cycle -> processes ending at that cycle

	// Sort log entries by cycle
	sort.Slice(logEntries, func(i, j int) bool {
		return logEntries[i].Cycle < logEntries[j].Cycle
	})

	for _, entry := range logEntries {
		fmt.Printf("Evaluating: %d:%s\n", entry.Cycle, entry.Process)

		// Complete any processes that finish at this cycle
		if endingProcesses, exists := runningProcesses[entry.Cycle]; exists {
			for _, endingProcess := range endingProcesses {
				process := processMap[endingProcess.Process]
				if process != nil {
					// Add results to stocks
					for _, result := range process.Results {
						stocks[result.Name] += result.Quantity
					}
				}
			}
			delete(runningProcesses, entry.Cycle)
		}

		// Check if the process exists
		process, exists := processMap[entry.Process]
		if !exists {
			return fmt.Errorf("at %d:%s unknown process", entry.Cycle, entry.Process)
		}

		// Check if we have enough resources
		for _, need := range process.Needs {
			if stocks[need.Name] < need.Quantity {
				return fmt.Errorf("at %d:%s stock insufficient", entry.Cycle, entry.Process)
			}
		}

		// Consume resources
		for _, need := range process.Needs {
			stocks[need.Name] -= need.Quantity
		}

		// Schedule process completion
		endCycle := entry.Cycle + process.Duration
		runningProcesses[endCycle] = append(runningProcesses[endCycle], entry)
	}

	// Complete any remaining processes
	for _, endingProcesses := range runningProcesses {
		for _, endingProcess := range endingProcesses {
			process := processMap[endingProcess.Process]
			if process != nil {
				for _, result := range process.Results {
					stocks[result.Name] += result.Quantity
				}
			}
		}
	}

	return nil
}
