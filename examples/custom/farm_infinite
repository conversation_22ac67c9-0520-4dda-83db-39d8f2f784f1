#
# farm_infinite - self-sustaining farm operation
# This configuration can rotate indefinitely
#
# stock
water:10
seeds:5
farmer:1
#
# processes
#
plant_crops:(seeds:1;water:2;farmer:1):(growing_crops:1):30
harvest_crops:(growing_crops:1;farmer:1):(food:3;seeds:2):10
collect_water:(farmer:1):(water:5):5
eat_food:(food:1):(energy:2):1
rest_farmer:(energy:1;farmer:1):(farmer:1):2
#
# optimize
#
optimize:(food)
