package main

import (
	"fmt"
	"time"
)

// Stock represents a resource with its quantity
type Stock struct {
	Name     string
	Quantity int
}

// Resource represents a needed or produced resource in a process
type Resource struct {
	Name     string
	Quantity int
}

// Process represents a production process
type Process struct {
	Name     string
	Needs    []Resource // Resources needed to start the process
	Results  []Resource // Resources produced by the process
	Duration int        // Number of cycles the process takes
}

// ProcessExecution represents a running instance of a process
type ProcessExecution struct {
	Process   *Process
	StartTime int
	EndTime   int
}

// Simulation represents the state of the simulation
type Simulation struct {
	Stocks            map[string]int      // Current stock levels
	Processes         []Process           // Available processes
	OptimizeTargets   []string            // What to optimize (time, stock names)
	RunningProcesses  []ProcessExecution  // Currently running processes
	CompletedSchedule []ProcessExecution  // Completed process schedule
	CurrentCycle      int                 // Current simulation cycle
	MaxWaitTime       time.Duration       // Maximum wait time for simulation
	StartTime         time.Time           // When simulation started
}

// Configuration represents the parsed configuration file
type Configuration struct {
	InitialStocks   map[string]int
	Processes       []Process
	OptimizeTargets []string
}

// LogEntry represents a single entry in the log file
type LogEntry struct {
	Cycle   int
	Process string
}

// ScheduleResult represents the result of scheduling
type ScheduleResult struct {
	Schedule    []ProcessExecution
	FinalStocks map[string]int
	LastCycle   int
	Error       error
}

// String methods for better display
func (s Stock) String() string {
	return fmt.Sprintf("%s => %d", s.Name, s.Quantity)
}

func (r Resource) String() string {
	return fmt.Sprintf("%s:%d", r.Name, r.Quantity)
}

func (p Process) String() string {
	return fmt.Sprintf("%s:(%v):(%v):%d", p.Name, p.Needs, p.Results, p.Duration)
}

func (pe ProcessExecution) String() string {
	return fmt.Sprintf("%d:%s", pe.StartTime, pe.Process.Name)
}
