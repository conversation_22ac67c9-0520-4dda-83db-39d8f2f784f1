package main

import (
	"fmt"
	"os"
	"sort"
	"strconv"
	"time"
)

func main() {
	if len(os.Args) != 3 {
		fmt.Println("Usage: ./stock_exchange <file> <waiting_time>")
		os.Exit(1)
	}

	configFile := os.Args[1]
	waitTimeStr := os.Args[2]

	// Parse waiting time
	waitTime, err := strconv.ParseFloat(waitTimeStr, 64)
	if err != nil {
		fmt.Printf("Error parsing waiting time: %v\n", err)
		fmt.Println("Exiting...")
		os.Exit(1)
	}

	maxWaitTime := time.Duration(waitTime * float64(time.Second))

	// Parse configuration
	config, err := ParseConfiguration(configFile)
	if err != nil {
		fmt.Println(err)
		fmt.Println("Exiting...")
		os.Exit(1)
	}

	// Create and run simulation
	simulation := NewSimulation(config, maxWaitTime)
	result := simulation.RunSimulation()

	if result.Error != nil {
		fmt.Printf("Simulation error: %v\n", result.Error)
		fmt.Println("Exiting...")
		os.Exit(1)
	}

	// Display results
	displayResults(result)

	// Generate log file
	err = generateLogFile(configFile, result)
	if err != nil {
		fmt.Printf("Error generating log file: %v\n", err)
		os.Exit(1)
	}
}

// displayResults displays the simulation results
func displayResults(result *ScheduleResult) {
	fmt.Println("Main Processes:")

	// Sort schedule by start time for display
	schedule := make([]ProcessExecution, len(result.Schedule))
	copy(schedule, result.Schedule)
	sort.Slice(schedule, func(i, j int) bool {
		return schedule[i].StartTime < schedule[j].StartTime
	})

	for _, pe := range schedule {
		fmt.Printf(" %d:%s\n", pe.StartTime, pe.Process.Name)
	}

	fmt.Printf("No more process doable at cycle %d\n", result.LastCycle)

	fmt.Println("Stock:")

	// Sort stocks by name for consistent display
	var stockNames []string
	for name := range result.FinalStocks {
		stockNames = append(stockNames, name)
	}
	sort.Strings(stockNames)

	for _, name := range stockNames {
		fmt.Printf(" %s => %d\n", name, result.FinalStocks[name])
	}
}

// generateLogFile generates a log file with the schedule
func generateLogFile(configFile string, result *ScheduleResult) error {
	logFile := configFile + ".log"

	file, err := os.Create(logFile)
	if err != nil {
		return fmt.Errorf("error creating log file: %v", err)
	}
	defer file.Close()

	// Sort schedule by start time
	schedule := make([]ProcessExecution, len(result.Schedule))
	copy(schedule, result.Schedule)
	sort.Slice(schedule, func(i, j int) bool {
		return schedule[i].StartTime < schedule[j].StartTime
	})

	// Write schedule entries
	for _, pe := range schedule {
		_, err := fmt.Fprintf(file, "%d:%s\n", pe.StartTime, pe.Process.Name)
		if err != nil {
			return fmt.Errorf("error writing to log file: %v", err)
		}
	}

	// Write completion message
	_, err = fmt.Fprintf(file, "No more process doable at cycle %d\n", result.LastCycle)
	if err != nil {
		return fmt.Errorf("error writing to log file: %v", err)
	}

	return nil
}

// Helper function to check if a string is in a slice
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// Helper function to get the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// Helper function to get the maximum of two integers
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}
