package main

import (
	"bufio"
	"fmt"
	"os"
	"regexp"
	"strconv"
	"strings"
)

// ParseConfiguration parses a configuration file and returns a Configuration struct
func ParseConfiguration(filename string) (*Configuration, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, fmt.Errorf("error opening file: %v", err)
	}
	defer file.Close()

	config := &Configuration{
		InitialStocks:   make(map[string]int),
		Processes:       []Process{},
		OptimizeTargets: []string{},
	}

	scanner := bufio.NewScanner(file)
	lineNum := 0

	for scanner.Scan() {
		lineNum++
		line := strings.TrimSpace(scanner.Text())

		// Skip empty lines and comments
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// Parse stock definitions (name:quantity)
		if stockMatch := regexp.MustCompile(`^([a-zA-Z_][a-zA-Z0-9_]*):(\d+)$`).FindStringSubmatch(line); stockMatch != nil {
			name := stockMatch[1]
			quantity, err := strconv.Atoi(stockMatch[2])
			if err != nil {
				return nil, fmt.Errorf("error parsing stock quantity at line %d: %v", lineNum, err)
			}
			config.InitialStocks[name] = quantity
			continue
		}

		// Parse optimize directive
		if strings.HasPrefix(line, "optimize:") {
			optimizeStr := strings.TrimPrefix(line, "optimize:")
			optimizeStr = strings.Trim(optimizeStr, "()")
			
			if optimizeStr == "" {
				return nil, fmt.Errorf("error while parsing `%s`", line)
			}

			targets := strings.Split(optimizeStr, ";")
			for _, target := range targets {
				target = strings.TrimSpace(target)
				if target != "" {
					config.OptimizeTargets = append(config.OptimizeTargets, target)
				}
			}
			continue
		}

		// Parse process definitions
		if strings.Contains(line, ":") && strings.Contains(line, "(") && strings.Contains(line, ")") {
			process, err := parseProcess(line)
			if err != nil {
				return nil, fmt.Errorf("error while parsing `%s`", line)
			}
			config.Processes = append(config.Processes, *process)
			continue
		}

		// If we reach here, the line format is not recognized
		return nil, fmt.Errorf("error while parsing `%s`", line)
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading file: %v", err)
	}

	// Validate configuration
	if len(config.Processes) == 0 {
		return nil, fmt.Errorf("missing processes")
	}

	if len(config.OptimizeTargets) == 0 {
		return nil, fmt.Errorf("missing optimization targets")
	}

	return config, nil
}

// parseProcess parses a process definition line
func parseProcess(line string) (*Process, error) {
	// Process format: name:(need1:qty1;need2:qty2;...):(result1:qty1;result2:qty2;...):duration
	processRegex := regexp.MustCompile(`^([a-zA-Z_][a-zA-Z0-9_]*):(\([^)]*\)):(\([^)]*\)):(\d+)$`)
	matches := processRegex.FindStringSubmatch(line)
	
	if matches == nil {
		return nil, fmt.Errorf("invalid process format")
	}

	name := matches[1]
	needsStr := strings.Trim(matches[2], "()")
	resultsStr := strings.Trim(matches[3], "()")
	duration, err := strconv.Atoi(matches[4])
	if err != nil {
		return nil, fmt.Errorf("invalid duration: %v", err)
	}

	needs, err := parseResources(needsStr)
	if err != nil {
		return nil, fmt.Errorf("error parsing needs: %v", err)
	}

	results, err := parseResources(resultsStr)
	if err != nil {
		return nil, fmt.Errorf("error parsing results: %v", err)
	}

	return &Process{
		Name:     name,
		Needs:    needs,
		Results:  results,
		Duration: duration,
	}, nil
}

// parseResources parses a resource list (resource1:qty1;resource2:qty2;...)
func parseResources(resourceStr string) ([]Resource, error) {
	var resources []Resource
	
	if resourceStr == "" {
		return resources, nil
	}

	parts := strings.Split(resourceStr, ";")
	for _, part := range parts {
		part = strings.TrimSpace(part)
		if part == "" {
			continue
		}

		resourceParts := strings.Split(part, ":")
		if len(resourceParts) != 2 {
			return nil, fmt.Errorf("invalid resource format: %s", part)
		}

		name := strings.TrimSpace(resourceParts[0])
		quantity, err := strconv.Atoi(strings.TrimSpace(resourceParts[1]))
		if err != nil {
			return nil, fmt.Errorf("invalid resource quantity: %v", err)
		}

		resources = append(resources, Resource{
			Name:     name,
			Quantity: quantity,
		})
	}

	return resources, nil
}
